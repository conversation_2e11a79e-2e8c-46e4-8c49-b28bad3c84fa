import random
import time



class Character:
    def __init__(self, name, health, attack, defense):
        self.name = name
        self.health = health
        self.attack = attack
        self.defense = defense

    def take_damage(self, damage):
        self.health -= damage
        if self.health < 0:
            self.health = 0

    def is_alive(self):
        return self.health > 0

class Player(Character):
    def __init__(self, name, health, attack, defense):
        super().__init__(name, health, attack, defense)
        self.inventory = []

    def add_item(self, item):
        self.inventory.append(item)

    def remove_item(self, item):
        if item in self.inventory:
            self.inventory.remove(item)

class Enemy(Character):
    def __init__(self, name, health, attack, defense):
        super().__init__(name, health, attack, defense)

    def attack(self, target):
        # Basic enemy attack with 1-20 damage
        damage = random.randint(1, 20)
        target.take_damage(damage)
        print(f"{self.name} attacks {target.name} for {damage} damage!")
        print(f"{target.name} has {target.health} health remaining.")


# Example usage and testing
if __name__ == "__main__":
    # Create a player and enemy
    player = Player("Hero", 100, 15, 5)
    enemy = Enemy("Goblin", 50, 10, 3)

    print("=== RPG Combat Demo ===")
    print(f"{player.name}: {player.health} HP")
    print(f"{enemy.name}: {enemy.health} HP")
    print()

    # Enemy attacks player a few times
    for i in range(3):
        if player.is_alive():
            print(f"Turn {i+1}:")
            enemy.attack(player)
            print()
            time.sleep(1)  # Small delay for dramatic effect
        else:
            print(f"{player.name} has been defeated!")
            break

    if player.is_alive():
        print(f"{player.name} survives the encounter!")