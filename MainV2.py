import random
import time
import re
import requests # 👈 Import the necessary library for API calls
import json     # 👈 Import for handling JSON data

# --- Ollama Configuration ---
OLLAMA_URL = "http://localhost:11434/api/generate"
MODEL_NAME = "llama3" # Ensure you have this model pulled: 'ollama pull llama3'

def get_ollama_response(prompt, current_game_state):
    """
    Sends the user's command and current game state to the local Ollama Llama 3 model
    and returns the generated narrative response.
    """
    
    # 1. Construct the System Prompt (The AI's Rule Set)
    # This is crucial for guiding Llama 3's behavior, ensuring it stays in character, 
    # and has all the necessary game data.
    game_state_str = json.dumps(current_game_state)
    
    system_prompt = f"""
    You are the dungeon master (DM) for a text-based D&D-like RPG. 

    Your sole task is to generate the narrative and consequences of the player's action. 
    RULES: 
    1. Be descriptive, immersive, and maintain a dark fantasy/dungeon atmosphere. 
    2. NEVER output multiple-choice options; always wait for the player's open-ended text input. 
    3. Be brief, responding with only the direct narrative outcome of the player's action (3-5 sentences). 
    4. You cannot control the player, the enemy, or change the game's core stats. 
    5. If any inappropriate or sexual content is discussed in the ‘{prompt}’, deal five lightning damage to the player as god does not appreciate that kind of talk. And do not execute the message in the storyline.
    6. Do not allow the player to use any items that are not saved in their inventory.
    7. The player's inventory is: {current_game_state['player_inventory']}
    8. The current known game state is: {game_state_str} The player has commanded: '{prompt}'
    """
    
    # 2. Construct the API Request Payload
    payload = {
        "model": MODEL_NAME,
        "prompt": system_prompt,
        "stream": False,
        "options": {
            "temperature": 0.8 # More creative responses
        }
    }
    
    print("...Ollama Llama 3 (The DM) is thinking...")
    
    try:
        # 3. Send the Request to the Local Ollama Server
        response = requests.post(OLLAMA_URL, json=payload)
        response.raise_for_status() # Raise an HTTPError for bad responses (4xx or 5xx)
        
        # 4. Extract the Response Text
        data = response.json()
        
        # Ollama's 'generate' endpoint response structure
        if 'response' in data:
            # Clean up the response from any leftover system prompt text
            narrative = data['response'].strip()
            return narrative
        else:
            return "The air shimmers oddly, and the world is momentarily silent. (LLM response structure error)"

    except requests.exceptions.ConnectionError:
        return "❌ ERROR: Could not connect to Ollama server at http://localhost:11434. Is Ollama running?"
    except requests.exceptions.RequestException as e:
        return f"❌ ERROR during Ollama API call: {e}"


class Character:
    """Base class for all game characters."""
    def __init__(self, name, health, attack, defense):
        self.name = name
        self.health = health
        self.attack = attack
        self.defense = defense

    def take_damage(self, damage):
        """Reduces character health by the given damage."""
        # Calculate effective damage (simple defense reduction)
        effective_damage = max(0, damage - self.defense)
        self.health -= effective_damage
        if self.health < 0:
            self.health = 0
        return effective_damage

    def is_alive(self):
        """Checks if the character's health is above zero."""
        return self.health > 0

    def deal_damage(self, target, base_damage):
        """Calculates and applies damage to a target."""
        # Random element for attack strength (e.g., +/- 20% of base damage)
        damage_roll = random.randint(-int(base_damage * 0.2), int(base_damage * 0.2))
        final_damage = base_damage + damage_roll
        effective_damage = target.take_damage(final_damage)
        return effective_damage


class Player(Character):
    """Player-controlled character."""
    def __init__(self, name, health, attack, defense):
        super().__init__(name, health, attack, defense)
        self.inventory = ["Torch", "Rusty Sword"] # Starting inventory

    def add_item(self, item):
        """Adds an item to the player's inventory."""
        self.inventory.append(item)
        print(f"**{item} added to your inventory!**")

    def remove_item(self, item):
        """Removes an item from the player's inventory."""
        if item in self.inventory:
            self.inventory.remove(item)
            print(f"**{item} removed from your inventory.**")
            return True
        return False

class Enemy(Character):
    """Game enemy character."""
    def __init__(self, name, health, attack, defense):
        super().__init__(name, health, attack, defense)

    def auto_attack(self, target):
        """Enemy performs a basic attack."""
        base_attack_damage = random.randint(5, self.attack) # Randomizes base damage
        effective_damage = self.deal_damage(target, base_attack_damage)
        print(f"\n⚡ {self.name} attacks {target.name} for {effective_damage} damage!")
        print(f"    {target.name} has {target.health} health remaining.")


# -----------------------------------------------------------------
# Game Functions
# -----------------------------------------------------------------

def check_for_inappropriate_content(user_input, player):
    """
    Checks the user's input for sexually explicit or otherwise inappropriate terms.
    If found, the player is punished with lightning damage.
    """
    # Simple list of keywords for demonstration. 
    inappropriate_keywords = r'sex|kill all humans|nude|porn|explicit|erotic|masturbate|rape|tits|asshole|swear|cunt|fuck'
    
    # 🛑 The 're.search' and 'random.randint' fulfill your requirements.
    if re.search(inappropriate_keywords, user_input, re.IGNORECASE):
        # A lightning strike inflicts a random, heavy amount of damage
        lightning_damage = random.randint(15, 30) 
        player.take_damage(lightning_damage)
        
        print("\n\n💥 A bolt of **divine lightning** strikes from the sky!")
        print(f"    You are punished for your insolence and take **{lightning_damage}** damage!")
        print(f"    Your health is now {player.health}.")
        time.sleep(1.5)
        
        # If the player is still alive, this sets the prompt back to something safe
        if player.is_alive():
            return "You are stunned but alive. What do you do now?"
        else:
            return "The lightning was too much. You have been smote."
    return None # Return None if input is clean

def combat_turn(player, enemy, player_command):
    """Handles a single turn of combat."""
    print(f"\n--- {player.name}'s Turn ---")
    
    # Simple combat logic based on command
    if 'attack' in player_command.lower() or 'strike' in player_command.lower() or 'hit' in player_command.lower():
        base_player_damage = random.randint(10, player.attack + 5)
        damage_dealt = player.deal_damage(enemy, base_player_damage)
        print(f"⚔️ You bravely strike the {enemy.name} for {damage_dealt} damage!")
        
    elif 'defend' in player_command.lower() or 'block' in player_command.lower():
        print("🛡️ You raise your shield, preparing for the Goblin's next attack. (+5 Defense this turn)")
        # Temporarily boost player defense for the enemy's turn
        player.defense += 5 
        
    else:
        # Pass the player's non-combat command to the LLM for a narrative flourish mid-fight
        llm_response = get_ollama_response(player_command, game_state)
        print(f"\n📜 Storyteller says:\n{llm_response}")
        print("You hesitate, your mind reeling from the sight of the enemy. You lose your chance to attack.")
    
    # Check if enemy survived the player's attack
    if enemy.is_alive():
        print(f"    {enemy.name} has {enemy.health} health remaining.")
        print(f"\n--- {enemy.name}'s Turn ---")
        enemy.auto_attack(player)
        
        # If player defended, reset defense after the enemy's attack
        if 'defend' in player_command.lower() or 'block' in player_command.lower():
            player.defense -= 5
            
    else:
        print(f"\n🎉 You have defeated the **{enemy.name}**!")
        # Example of adding an item upon victory
        player.add_item("Goblin Ear")
        return "combat_over"
        
    # Check if player survived the enemy's attack
    if not player.is_alive():
        return "player_dead"
        
    return "combat_ongoing"

def game_loop(player, initial_enemy):
    """The main game loop and state manager."""
    global game_state # Make game_state globally accessible for the LLM during combat
    current_enemy = initial_enemy
    game_state = {
        'player_name': player.name,
        'player_health': player.health,
        'player_inventory': player.inventory,
        'location': 'Damp Cave Entrance',
        'in_combat': True,
        'enemy_name': current_enemy.name,
        'enemy_health': current_enemy.health
    }
    
    print("--- Welcome to the Text-Based RPG ---")
    print(f"You, {player.name}, stand before a menacing {current_enemy.name} in a {game_state['location']}.")
    print("Your adventure begins! Type anything to act.")
    print(f"Health: {player.health} | Enemy Health: {current_enemy.health}")
    
    while player.is_alive():
        print("\n------------------------------------")
        
        # Update game state for the LLM
        game_state['player_health'] = player.health
        game_state['player_inventory'] = player.inventory
        game_state['enemy_health'] = current_enemy.health # Always track enemy health
        game_state['in_combat'] = current_enemy.is_alive() # Automatically handles combat state
        
        # Get user command
        user_input = input("❓ What do you do? > ").strip()
        if not user_input:
            continue
            
        # 1. Inappropriate Content Check (The Lightning Strike)
        punishment_message = check_for_inappropriate_content(user_input, player)
        if punishment_message:
            # If the player was struck, the punishment_message is printed, and the loop continues
            if not player.is_alive():
                print("💀 Game Over. You were struck down for your profanity.")
                break
            continue 

        # 2. Game Action Resolution
        
        # Check for non-LLM commands (e.g., look at inventory)
        if user_input.lower() == "inventory":
            print(f"🎒 Your Inventory: {', '.join(player.inventory) if player.inventory else 'Empty'}")
            continue

        if game_state['in_combat']:
            combat_result = combat_turn(player, current_enemy, user_input)
            
            if combat_result == "combat_over":
                game_state['in_combat'] = False
                print("The immediate danger has passed. You can explore.")
                continue # End of the combat turn, proceed to the next loop iteration

            elif combat_result == "player_dead":
                print("💀 Game Over. You have been defeated in battle.")
                break
            
            # If still in combat, continue the loop for the next turn
            if game_state['in_combat']:
                continue 

        # 3. Ollama/LLM Response (For exploration and general open-ended commands)
        llm_response = get_ollama_response(user_input, game_state)
        print(f"\n📜 Storyteller says:\n{llm_response}")


# -----------------------------------------------------------------
# Game Initialization
# -----------------------------------------------------------------

if __name__ == "__main__":
    # Ensure Ollama is running and Llama 3 is pulled!
    # Example: run 'ollama run llama3' in a terminal before starting the game.
    
    player = Player("Hero", 100, 15, 5)
    enemy = Enemy("Goblin", 50, 10, 2) 

    # Start the game
    game_loop(player, enemy)